import aiofiles
from aiofiles import os
import fitz  # PyMuPDF
import re
from Configs.Config import SysConfig
from Services.ContentLoader.LoaderFactory import LoaderFactory
from Services.TaskServer.BaseGenTask import BaseGenTask
from Utils.FileDownloader import FileDownloader


class TalentParseWorker(BaseGenTask):
    def __init__(self, fileUrl: str):
        super().__init__(fileUrl)
        # 服务实例
        self.temp_dir = SysConfig["talent"]["file_dir"]
        self.file_downloader = FileDownloader(self.temp_dir)

    async def _task(self):

        # TODO: 后续增加队列和任务管理表，目前先简单实现转换

        # 异步下载文件（避免阻塞其他请求）
        local_file_url = await self.file_downloader.download_in_thread(self._file_url)
        if not local_file_url:
            raise FileNotFoundError(f"文件下载失败: {self._file_url}")

        # 基础校验
        if not await aiofiles.os.path.exists(local_file_url):
            raise FileNotFoundError(f"文件不存在: {local_file_url}")
        # 创建内容块
        content = ""
        # 获取文件后缀
        file_extension = local_file_url.split('.')[-1].lower()
        if file_extension == 'pdf':
            # 在线程池中执行PDF解析（避免阻塞）
            import asyncio
            loop = asyncio.get_event_loop()
            content += await loop.run_in_executor(None, parse_pdf, local_file_url)
        else:
            # 在线程池中执行文件加载（避免阻塞）
            import asyncio
            loop = asyncio.get_event_loop()
            content += await loop.run_in_executor(None, self._load_file_content, local_file_url)

        # 移除临时文件
        # if os.path.exists(local_file_url):
        #     self.file_downloader.remove(local_file_url)
        # 移除临时文件
        self.file_downloader.remove(local_file_url)
        return content

    def _load_file_content(self, local_file_url: str) -> str:
        """在线程池中执行的文件加载方法"""
        # 初始化文件加载器
        loader = LoaderFactory.get_file_loader(local_file_url, 400)
        if not loader:
            raise ValueError(f"不支持的文件类型: {local_file_url}")
        # 开始任务
        docs = loader.load(file_name="")
        if not docs:
            raise ValueError(f"文件加载失败: {local_file_url}")
        content = ""
        for doc in docs:
            content += doc.page_content
        return content

    def _update_task_state(self, *args, **kwargs):
        """
        实现抽象方法 _update_task_state。
        这里可以根据实际业务需求添加具体逻辑，
        目前暂时不做任何操作。
        """
        pass

# 解析pdf
def parse_pdf(file_path):
    # 打开PDF文件
    doc = fitz.open(file_path)

    # 遍历每一页
    text = ""
    for page_num in range(len(doc)):  # 遍历所有页面
        page = doc.load_page(page_num)  # 加载当前页面
        # 提取当前页的文字
        text += page.get_text("text")
    return clean_text_final(text)

def clean_text_final(text):
    # 替换特殊符号
    text = re.sub(r'[●•→/|◆■©]+', ' ', text)
    # 合并空格和换行
    text = re.sub(r'\s+', ' ', text)
    # 去除首尾空白
    text = text.strip()
    # 去重行
    lines = [line.strip() for line in text.split('\n') if len(line.strip()) > 3]
    return '\n'.join(lines)