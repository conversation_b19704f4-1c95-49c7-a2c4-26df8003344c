import asyncio
from concurrent.futures import Thread<PERSON>oolExecutor

from Agents.ImproveInfoAgent import ImproveInfoAgent
from Utils.AsyncConfig import async_config
from Utils.logs.LoggingConfig import logger


class AsyncImproveInfoAgent:
    """异步ImproveInfoAgent包装器，避免LLM调用阻塞其他接口"""
    
    def __init__(self):
        self._improve_agent = ImproveInfoAgent()

    async def evaluate_improve_info_async(self, evaluate: str):
        """异步版本的evaluateImproveInfo"""
        return await async_config.run_with_llm_limit(
            self._improve_agent.evaluateImproveInfo,
            evaluate
        )

    def cleanup(self):
        """清理资源"""
        try:
            # 资源清理由async_config统一管理
            logger.info("AsyncImproveInfoAgent资源清理完成")
        except Exception as e:
            logger.error(f"清理AsyncImproveInfoAgent资源时出错: {e}")


# 创建全局异步代理实例
async_improve_info_agent = AsyncImproveInfoAgent()
