"""
异步TalentAgent代理类
将原有的同步LLM调用包装为异步操作，避免阻塞事件循环
"""
import asyncio
from typing import Optional
from concurrent.futures import ThreadPoolExecutor

from Agents.TalentAgent import TalentAgent
from Agents.TalentAgentProcessing import TalentAgentProcessing
from Models.agent.ResumeInfo import ResumeInfo
from Models.dto.TaskInfoDto import TaskInfoDto
from Utils.AsyncConfig import async_config
from Utils.logs.LoggingConfig import logger


class AsyncTalentAgent:
    """异步TalentAgent代理类"""
    
    def __init__(self):
        self._talent_agent = TalentAgent()
        self._talent_processor = TalentAgentProcessing()
        try:
            self._executor = async_config.get_llm_executor()
        except Exception as e:
            logger.error(f"初始化异步配置失败: {e}")
            # 使用默认的线程池
            from concurrent.futures import ThreadPoolExecutor
            self._executor = ThreadPoolExecutor(max_workers=3, thread_name_prefix="Fallback-LLM")
    
    async def chat_for_answer_async(self, question: str, id: int = None) -> int:
        """异步版本的聊天回答"""
        try:
            # 调用正确的聊天方法
            return await self._talent_agent.chat_for_question(question, id)
        except Exception as e:
            logger.error(f"Error in async_chat_for_answer: {str(e)}")
            return 0

    def sync_chat_for_answer(self, question: str, id: int = None) -> int:
        """同步版本的聊天回答（用于线程池）"""
        try:
            # 直接调用原始的async方法
            import asyncio
            return asyncio.run(self._talent_agent.chat_for_answer(question, id))
        except Exception as e:
            logger.error(f"Error in sync_chat_for_answer: {str(e)}")
            return 0

    async def formatting_async(self, content: str, condition: str = None, boss_flag: bool = False) -> Optional[ResumeInfo]:
        """异步版本的简历格式化"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self._executor,
            self._talent_agent._formatting,
            content,
            condition,
            boss_flag
        )

    async def formatting_processing_async(self, content: str, condition: str = None, boss_flag: bool = False) -> Optional[ResumeInfo]:
        """异步版本的TalentAgentProcessing格式化"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self._executor,
            self._talent_processor._formatting,
            content,
            condition,
            boss_flag
        )

    async def calculate_total_score_async(self, talent_info: ResumeInfo, task_info: TaskInfoDto) -> float:
        """异步版本的总分计算"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self._executor,
            self._talent_agent.calculate_total_score,
            talent_info,
            task_info
        )
    
    # 同步方法的直接代理
    def getPostingInfo(self, job_id: int):
        return self._talent_agent.getPostingInfo(job_id)
    
    def getAnalyseTaskInfo(self, task_id: int):
        return self._talent_agent.getAnalyseTaskInfo(task_id)
    
    def updateFileStatus(self, file_id: int, status: int):
        return self._talent_agent.updateFileStatus(file_id, status)
    
    def saveAnalyseTaskInfo(self, talent_info: ResumeInfo) -> bool:
        return self._talent_agent.saveAnalyseTaskInfo(talent_info)
    
    def getConversationInfo(self, id: int):
        # TalentAgent没有这个方法，返回None
        logger.warning(f"getConversationInfo方法不存在，ID: {id}")
        return None


# 全局异步代理实例
async_talent_agent = AsyncTalentAgent()
