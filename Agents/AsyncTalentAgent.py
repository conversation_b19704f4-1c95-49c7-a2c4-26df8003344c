import asyncio
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON><PERSON>xecutor
from typing import Optional

from Agents.TalentAgent import TalentAgent
from Agents.TalentAgentProcessing import TalentAgentProcessing
from Models.dto.TaskInfoDto import TaskInfoDto
from Models.pojo.ResumeInfo import ResumeInfo
from Utils.AsyncConfig import async_config
from Utils.logs.LoggingConfig import logger


class AsyncTalentAgent:
    """异步TalentAgent包装器，避免LLM调用阻塞其他接口"""
    
    def __init__(self):
        self._talent_agent = TalentAgent()
        self._talent_processor = TalentAgentProcessing()
    
    async def chat_for_answer_async(self, content: str, job_id: int = None):
        """异步版本的chat_for_answer"""
        return await async_config.run_with_llm_limit(
            self._talent_agent.chat_for_answer,
            content,
            job_id
        )
    
    async def formatting_async(self, content: str, condition: str = None, boss_flag: bool = False) -> Optional[ResumeInfo]:
        """异步版本的简历格式化"""
        return await async_config.run_with_llm_limit(
            self._talent_agent._formatting,
            content,
            condition,
            boss_flag
        )

    async def formatting_processing_async(self, content: str, condition: str = None, boss_flag: bool = False) -> Optional[ResumeInfo]:
        """异步版本的TalentAgentProcessing格式化"""
        return await async_config.run_with_llm_limit(
            self._talent_processor._formatting,
            content,
            condition,
            boss_flag
        )

    async def calculate_total_score_async(self, talent_info: ResumeInfo, task_info: TaskInfoDto) -> float:
        """异步版本的总分计算"""
        return await async_config.run_with_llm_limit(
            self._talent_agent.calculate_total_score,
            talent_info,
            task_info
        )
    
    # 同步方法的直接代理
    def getPostingInfo(self, job_id: int):
        return self._talent_agent.getPostingInfo(job_id)
    
    def getAnalyseTaskInfo(self, task_id: int):
        return self._talent_agent.getAnalyseTaskInfo(task_id)
    
    def updateFileStatus(self, file_id: int, status: int):
        return self._talent_agent.updateFileStatus(file_id, status)
    
    def saveAnalyseTaskInfo(self, talent_info: ResumeInfo):
        return self._talent_agent.saveAnalyseTaskInfo(talent_info)
    
    def condition_filter(self, talent_info: ResumeInfo, job_id: int = None, requirements: TaskInfoDto = None):
        return self._talent_agent.condition_filter(talent_info, job_id, requirements)
    
    def calculate_total_score(self, talent_info: ResumeInfo, task_info: TaskInfoDto) -> float:
        return self._talent_agent.calculate_total_score(talent_info, task_info)
    
    def cleanup(self):
        """清理资源"""
        try:
            # 资源清理由async_config统一管理
            logger.info("AsyncTalentAgent资源清理完成")
        except Exception as e:
            logger.error(f"清理AsyncTalentAgent资源时出错: {e}")


# 创建全局异步代理实例
async_talent_agent = AsyncTalentAgent()
