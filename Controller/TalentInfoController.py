from fastapi import APIRouter, BackgroundTasks
from pydantic import BaseModel
from urllib.parse import unquote
import asyncio

from Agents.ImproveInfoAgent import ImproveInfoAgent
from Agents.AsyncImproveInfoAgent import async_improve_info_agent
from Models.dto.TaskInfoDto import TaskInfoDto

from Agents.TalentAgent import TalentAgent
from Agents.TalentAgentProcessing import TalentAgentProcessing
from Agents.AsyncTalentAgent import async_talent_agent
from Models.AjaxResult import AjaxResult
from Services.TaskServer.TalentParseWorker import TalentParseWorker
from Utils.TaskManager import task_manager

# 添加项目根目录到系统路径
router = APIRouter(prefix="/agentService/api/talent", tags=["talent"])


class TalentInfo(BaseModel):
    id: int = None
    content: str = None
    question: str = None


@router.post("/info")
async def chat(request: TalentInfo):
    try:
        if not request.question:
            raise AjaxResult.error()

        # 使用异步版本避免阻塞其他接口
        row = await async_talent_agent.chat_for_answer_async(request.question, request.id)
        return AjaxResult.success(row)
    except Exception as e:
        print(f"Error in stream_chat: {str(e)}")
        return AjaxResult.error(data=0, message=str(e))


# 简历解析
@router.get("/parse")
async def parse(fileUrl: str = None):
    try:
        if not fileUrl:
            raise AjaxResult.error("文件地址不能为空")

        # 创建一个任务在后台执行，立即返回
        task = asyncio.create_task(parse_resume_background(fileUrl))

        # 等待一小段时间，如果快速完成就直接返回结果
        try:
            result = await asyncio.wait_for(task, timeout=2.0)  # 2秒超时
            return result
        except asyncio.TimeoutError:
            # 如果超时，返回处理中状态，让前端轮询
            return AjaxResult.success({"status": "processing", "message": "简历解析中，请稍后查询结果"})

    except Exception as e:
        print(f"Error in parse: {str(e)}")
        return AjaxResult.error(str(e))

async def parse_resume_background(fileUrl: str):
    """后台执行简历解析"""
    try:
        # 调用文件解析方法
        content = await TalentParseWorker(fileUrl)._task()
        if not content:
            raise Exception("文件解析失败")
        # 使用异步版本避免阻塞其他接口
        talent_info = await async_talent_agent.formatting_async(content)
        return AjaxResult.success(talent_info)
    except Exception as e:
        print(f"Error in parse_resume_background: {str(e)}")
        return AjaxResult.error(str(e))

# 简历评分
@router.get("/rating")
def rating():
    try:
        # TODO: 评分逻辑,直接传json评分结果
        return AjaxResult.success()
    except Exception as e:
        print(f"Error in stream_chat: {str(e)}")
        return AjaxResult.error(str(e))


# 简历解析
@router.get("/parseDto")
async def parseDto(fileUrl: str = None):
    try:
        if not fileUrl:
            raise AjaxResult.error("文件地址不能为空")

        # 创建一个任务在后台执行，立即返回
        task = asyncio.create_task(parse_dto_background(fileUrl))

        # 等待一小段时间，如果快速完成就直接返回结果
        try:
            result = await asyncio.wait_for(task, timeout=2.0)  # 2秒超时
            return result
        except asyncio.TimeoutError:
            # 如果超时，返回处理中状态，让前端轮询
            return AjaxResult.success({"status": "processing", "message": "简历解析中，请稍后查询结果"})

    except Exception as e:
        print(f"Error in parseDto: {str(e)}")
        return AjaxResult.error(str(e))

async def parse_dto_background(fileUrl: str):
    """后台执行简历解析DTO"""
    try:
        # 调用文件解析方法
        content = await TalentParseWorker(fileUrl)._task()
        if not content:
            raise Exception("文件解析失败")
        # 使用异步版本避免阻塞其他接口
        talent_info = await async_talent_agent.formatting_processing_async(content, boss_flag=True)
        return AjaxResult.success(talent_info)
    except Exception as e:
        print(f"Error in parse_dto_background: {str(e)}")
        return AjaxResult.error(str(e))

# 简历分析
@router.get("/analyse")
async def analyse(fileUrl: str = None, taskId: int = None, id: int = None):
    try:
        if not fileUrl:
            raise AjaxResult.error("文件地址不能为空")

        # 标记解析中状态=1
        if taskId is not None:
            await async_talent_agent.updateFileStatus_async(id, 1)

        # 创建一个任务在后台执行
        task = asyncio.create_task(analyse_background(fileUrl, taskId, id))

        # 等待一小段时间，如果快速完成就直接返回结果
        try:
            result = await asyncio.wait_for(task, timeout=3.0)  # 3秒超时
            return result
        except asyncio.TimeoutError:
            # 如果超时，返回处理中状态，让前端轮询
            return AjaxResult.success({"status": "processing", "message": "简历分析中，请稍后查询结果"})

    except Exception as e:
        # 异常统一标记失败
        if taskId is not None:
            try:
                await async_talent_agent.updateFileStatus_async(id, 2)
            except Exception:
                pass
        print(f"Error in analyse: {str(e)}")
        return AjaxResult.error(str(e))

async def analyse_background(fileUrl: str, taskId: int = None, id: int = None):
    """后台执行简历分析"""
    try:
        # 调用文件解析方法
        content = await TalentParseWorker(fileUrl)._task()
        if not content:
            # 解析失败，标记失败状态=2
            if taskId is not None:
                await async_talent_agent.updateFileStatus_async(id, 2)
            raise Exception("文件解析失败")

        # 使用异步版本格式化并评分
        talent_info = await async_talent_agent.formatting_async(content)
        if talent_info is None:
            # 解析失败，标记失败状态=2
            if taskId is not None:
                await async_talent_agent.updateFileStatus_async(id, 2)
            raise Exception("简历解析失败")

        task_dto = await async_talent_agent.getAnalyseTaskInfo_async(taskId)
        if not task_dto:
            if taskId is not None:
                await async_talent_agent.updateFileStatus_async(id, 2)
            raise Exception("获取岗位信息失败，无法评分")

        taskInfo = TaskInfoDto.from_dict(task_dto)
        # 使用异步版本计算总分
        total_score = await async_talent_agent.calculate_total_score_async(talent_info, taskInfo)
        talent_info.totalScore = total_score

        talent_info.fileId = id
        talent_info.jobId = taskId
        # 保存解析结果
        save_success = await async_talent_agent.saveAnalyseTaskInfo_async(talent_info)

        # 根据保存结果更新状态
        if taskId is not None:
            if save_success:
                # 保存成功，更新状态为成功=0
                await async_talent_agent.updateFileStatus_async(id, 0)
            else:
                # 保存失败，更新状态为失败=2
                await async_talent_agent.updateFileStatus_async(id, 2)
                raise Exception("保存分析结果失败")

        return AjaxResult.success(talent_info)
    except Exception as e:
        # 异常统一标记失败
        if taskId is not None:
            try:
                await async_talent_agent.updateFileStatus_async(id, 2)
            except Exception:
                pass
        print(f"Error in analyse_background: {str(e)}")
        return AjaxResult.error(str(e))

@router.get("/improveInfo")
async def improveInfo(evaluate: str):
    evaluate = unquote(evaluate)
    try:
        if not evaluate:
            raise AjaxResult.error()
        # 使用异步版本避免阻塞其他接口
        body = await async_improve_info_agent.evaluate_improve_info_async(evaluate)
        return AjaxResult.success(body)
    except Exception as e:
        print(f"Error in stream_chat: {str(e)}")
        return AjaxResult.error(data=0, message="完善信息失败")


# 新增：非阻塞的简历解析接口
@router.post("/parse-async")
async def parse_async(request: dict):
    """非阻塞的简历解析接口"""
    try:
        file_url = request.get("fileUrl")
        if not file_url:
            return AjaxResult.error("文件地址不能为空")

        # 提交任务到后台执行
        task_id = task_manager.submit_task(sync_parse_resume, file_url)

        return AjaxResult.success({
            "task_id": task_id,
            "message": "简历解析任务已提交，请使用task_id查询结果"
        })

    except Exception as e:
        print(f"Error in parse_async: {str(e)}")
        return AjaxResult.error(str(e))


@router.get("/task-status/{task_id}")
async def get_task_status(task_id: str):
    """获取任务状态"""
    try:
        status = task_manager.get_task_status(task_id)
        if status:
            return AjaxResult.success(status)
        else:
            return AjaxResult.error("任务不存在")
    except Exception as e:
        print(f"Error in get_task_status: {str(e)}")
        return AjaxResult.error(str(e))


def sync_parse_resume(file_url: str):
    """同步执行简历解析（在线程池中运行）"""
    import asyncio

    # 在新的事件循环中运行异步代码
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    try:
        # 执行解析
        content = loop.run_until_complete(TalentParseWorker(file_url)._task())
        if not content:
            raise Exception("文件解析失败")

        # 格式化简历
        talent_info = loop.run_until_complete(async_talent_agent.formatting_async(content))
        if not talent_info:
            raise Exception("简历格式化失败")

        return talent_info.dict() if hasattr(talent_info, 'dict') else talent_info

    finally:
        loop.close()